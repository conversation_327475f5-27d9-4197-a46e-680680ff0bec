#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进攻一团表现分析程序
分析进攻一团各职业玩家在对局中的表现，并汇总关键结果到失败原因分析报告
"""

import json
import statistics
from typing import Dict, List, Tuple, Any

class AttackTeamAnalyzer:
    def __init__(self, data_file: str, team_list_file: str):
        """初始化分析器"""
        self.data_file = data_file
        self.team_list_file = team_list_file
        self.player_data = []
        self.attack_team_members = []
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        # 加载对局数据
        with open(self.data_file, 'r', encoding='utf-8') as f:
            self.player_data = json.load(f)
            
        # 加载进攻一团名单
        with open(self.team_list_file, 'r', encoding='utf-8') as f:
            team_list = f.read().strip()
            self.attack_team_members = [name.strip() for name in team_list.split(',')]
            
        print(f"加载了 {len(self.player_data)} 名玩家数据")
        print(f"进攻一团成员: {len(self.attack_team_members)} 人")
        
    def get_attack_team_data(self) -> List[Dict]:
        """获取进攻一团成员数据"""
        attack_team_data = []
        for player in self.player_data:
            if player['玩家名字'] in self.attack_team_members:
                attack_team_data.append(player)
        return attack_team_data
        
    def classify_player_role(self, player: Dict) -> str:
        """根据数据特征分类玩家角色"""
        player_damage = player['对玩家伤害']
        building_damage = player['对建筑伤害']
        healing = player['治疗值']
        resource_gain = player['资源获取数']
        
        # 素问玩家：治疗值高，不负责拆塔
        if healing > 50000000:  # 5000万治疗值以上
            return "治疗师(素问)"
            
        # 资源获取专员：资源获取高，KDA低正常
        if resource_gain > 1000:
            return "资源获取专员"
            
        # 保镖玩家：对玩家伤害高于对建筑伤害
        if player_damage > building_damage and player_damage > 10000000:
            return "保镖玩家"
            
        # 拆塔专员：对建筑伤害高
        if building_damage > player_damage:
            return "拆塔专员"
            
        # 其他情况
        return "综合型玩家"
        
    def calculate_performance_metrics(self, player: Dict) -> Dict:
        """计算玩家表现指标"""
        kills = player['击败数']
        deaths = max(player['死亡数'], 1)  # 避免除零
        assists = player['助攻数']
        player_damage = player['对玩家伤害']
        building_damage = player['对建筑伤害']
        healing = player['治疗值']
        damage_taken = player['承受伤害']
        
        # 基础指标
        kd_ratio = kills / deaths
        total_damage = player_damage + building_damage
        
        # 拆塔专注度
        if total_damage > 0:
            building_focus = building_damage / total_damage
        else:
            building_focus = 0
            
        # 生存效率（每次死亡的伤害输出）
        damage_per_death = total_damage / deaths
        
        # 团队贡献度
        team_contribution = (kills * 2 + assists) / deaths
        
        return {
            'KD比': round(kd_ratio, 2),
            '拆塔专注度': round(building_focus * 100, 1),
            '每死亡伤害': int(damage_per_death),
            '团队贡献度': round(team_contribution, 2),
            '总伤害': total_damage
        }
        
    def analyze_role_performance(self, attack_team_data: List[Dict]) -> Dict:
        """分析各角色表现"""
        role_analysis = {}
        
        for player in attack_team_data:
            role = self.classify_player_role(player)
            metrics = self.calculate_performance_metrics(player)
            
            if role not in role_analysis:
                role_analysis[role] = []
                
            player_info = {
                '玩家名字': player['玩家名字'],
                '职业': player['职业'],
                '击败数': player['击败数'],
                '死亡数': player['死亡数'],
                '对玩家伤害': player['对玩家伤害'],
                '对建筑伤害': player['对建筑伤害'],
                '治疗值': player['治疗值'],
                '资源获取数': player['资源获取数'],
                **metrics
            }
            role_analysis[role].append(player_info)
            
        return role_analysis
        
    def identify_key_issues(self, attack_team_data: List[Dict]) -> Dict:
        """识别关键问题"""
        issues = {
            '高死亡率玩家': [],
            '低效率玩家': [],
            '角色定位问题': [],
            '资源配置问题': []
        }
        
        # 计算团队平均死亡数
        avg_deaths = statistics.mean([p['死亡数'] for p in attack_team_data])
        
        for player in attack_team_data:
            player_name = player['玩家名字']
            deaths = player['死亡数']
            role = self.classify_player_role(player)
            metrics = self.calculate_performance_metrics(player)
            
            # 高死亡率问题（超过平均死亡数1.5倍）
            if deaths > avg_deaths * 1.5:
                issues['高死亡率玩家'].append({
                    '玩家': player_name,
                    '职业': player['职业'],
                    '死亡数': deaths,
                    'KD比': metrics['KD比'],
                    '角色': role
                })
                
            # 低效率问题（拆塔专注度低于30%且不是治疗师）
            if metrics['拆塔专注度'] < 30 and role != "治疗师(素问)":
                issues['低效率玩家'].append({
                    '玩家': player_name,
                    '职业': player['职业'],
                    '拆塔专注度': metrics['拆塔专注度'],
                    '角色': role
                })
                
            # 角色定位问题（素问在进攻一团）
            if role == "治疗师(素问)":
                issues['角色定位问题'].append({
                    '玩家': player_name,
                    '职业': player['职业'],
                    '问题': '素问不应分配到进攻一团',
                    '治疗值': player['治疗值']
                })
                
        return issues
        
    def calculate_team_stats(self, attack_team_data: List[Dict]) -> Dict:
        """计算团队整体统计"""
        total_kills = sum(p['击败数'] for p in attack_team_data)
        total_deaths = sum(p['死亡数'] for p in attack_team_data)
        total_building_damage = sum(p['对建筑伤害'] for p in attack_team_data)
        total_player_damage = sum(p['对玩家伤害'] for p in attack_team_data)
        total_damage = total_building_damage + total_player_damage
        
        avg_deaths = statistics.mean([p['死亡数'] for p in attack_team_data])
        avg_building_focus = statistics.mean([
            self.calculate_performance_metrics(p)['拆塔专注度'] 
            for p in attack_team_data
        ])
        
        return {
            '团队人数': len(attack_team_data),
            '总击败数': total_kills,
            '总死亡数': total_deaths,
            '团队KD比': round(total_kills / max(total_deaths, 1), 2),
            '总建筑伤害': total_building_damage,
            '总玩家伤害': total_player_damage,
            '总伤害': total_damage,
            '平均死亡数': round(avg_deaths, 1),
            '平均拆塔专注度': round(avg_building_focus, 1),
            '建筑伤害占比': round(total_building_damage / max(total_damage, 1) * 100, 1)
        }

    def generate_detailed_report(self) -> str:
        """生成详细分析报告"""
        attack_team_data = self.get_attack_team_data()
        role_analysis = self.analyze_role_performance(attack_team_data)
        key_issues = self.identify_key_issues(attack_team_data)
        team_stats = self.calculate_team_stats(attack_team_data)

        report = []
        report.append("# 进攻一团详细表现分析报告\n")

        # 团队整体统计
        report.append("## 1. 团队整体统计")
        report.append(f"- **团队规模**: {team_stats['团队人数']} 人")
        report.append(f"- **团队KD比**: {team_stats['团队KD比']}")
        report.append(f"- **总击败数**: {team_stats['总击败数']}")
        report.append(f"- **总死亡数**: {team_stats['总死亡数']}")
        report.append(f"- **平均死亡数**: {team_stats['平均死亡数']} 次/人")
        report.append(f"- **总建筑伤害**: {team_stats['总建筑伤害']:,}")
        report.append(f"- **总玩家伤害**: {team_stats['总玩家伤害']:,}")
        report.append(f"- **建筑伤害占比**: {team_stats['建筑伤害占比']}%")
        report.append(f"- **平均拆塔专注度**: {team_stats['平均拆塔专注度']}%\n")

        # 各角色表现分析
        report.append("## 2. 各角色表现分析\n")
        for role, players in role_analysis.items():
            report.append(f"### {role} ({len(players)}人)\n")

            # 角色统计
            role_kills = sum(p['击败数'] for p in players)
            role_deaths = sum(p['死亡数'] for p in players)
            role_building_damage = sum(p['对建筑伤害'] for p in players)
            role_avg_focus = statistics.mean([p['拆塔专注度'] for p in players])

            report.append(f"**角色整体表现:**")
            report.append(f"- 总击败数: {role_kills}")
            report.append(f"- 总死亡数: {role_deaths}")
            report.append(f"- 角色KD比: {round(role_kills/max(role_deaths,1), 2)}")
            report.append(f"- 总建筑伤害: {role_building_damage:,}")
            report.append(f"- 平均拆塔专注度: {round(role_avg_focus, 1)}%\n")

            # 个人详细数据
            report.append("**个人详细数据:**\n")
            report.append("| 玩家 | 职业 | 击败 | 死亡 | KD比 | 建筑伤害 | 拆塔专注度 | 团队贡献度 |")
            report.append("|------|------|------|------|------|----------|------------|------------|")

            # 按拆塔专注度排序
            sorted_players = sorted(players, key=lambda x: x['拆塔专注度'], reverse=True)
            for player in sorted_players:
                report.append(f"| {player['玩家名字']} | {player['职业']} | {player['击败数']} | {player['死亡数']} | {player['KD比']} | {player['对建筑伤害']:,} | {player['拆塔专注度']}% | {player['团队贡献度']} |")
            report.append("")

        # 关键问题分析
        report.append("## 3. 关键问题分析\n")

        # 高死亡率问题
        if key_issues['高死亡率玩家']:
            report.append("### 3.1 高死亡率问题 (严重)")
            report.append(f"**问题描述**: {len(key_issues['高死亡率玩家'])} 名玩家死亡数过高\n")
            report.append("| 玩家 | 职业 | 死亡数 | KD比 | 角色定位 |")
            report.append("|------|------|--------|------|----------|")
            for player in sorted(key_issues['高死亡率玩家'], key=lambda x: x['死亡数'], reverse=True):
                report.append(f"| {player['玩家']} | {player['职业']} | {player['死亡数']} | {player['KD比']} | {player['角色']} |")
            report.append("")

        # 低效率问题
        if key_issues['低效率玩家']:
            report.append("### 3.2 拆塔效率问题 (严重)")
            report.append(f"**问题描述**: {len(key_issues['低效率玩家'])} 名玩家拆塔专注度不足\n")
            report.append("| 玩家 | 职业 | 拆塔专注度 | 角色定位 |")
            report.append("|------|------|------------|----------|")
            for player in sorted(key_issues['低效率玩家'], key=lambda x: x['拆塔专注度']):
                report.append(f"| {player['玩家']} | {player['职业']} | {player['拆塔专注度']}% | {player['角色']} |")
            report.append("")

        # 角色定位问题
        if key_issues['角色定位问题']:
            report.append("### 3.3 角色配置问题 (严重)")
            report.append(f"**问题描述**: {len(key_issues['角色定位问题'])} 名素问被错误分配到进攻一团\n")
            report.append("| 玩家 | 职业 | 治疗值 | 问题描述 |")
            report.append("|------|------|--------|----------|")
            for player in key_issues['角色定位问题']:
                report.append(f"| {player['玩家']} | {player['职业']} | {player['治疗值']:,} | {player['问题']} |")
            report.append("")

        return "\n".join(report)

    def update_failure_analysis_report(self, report_file: str):
        """更新失败原因分析报告"""
        # 生成进攻一团分析内容
        attack_team_data = self.get_attack_team_data()
        team_stats = self.calculate_team_stats(attack_team_data)
        key_issues = self.identify_key_issues(attack_team_data)

        # 读取现有报告
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 准备要插入的新内容
        new_section = self._generate_summary_for_report(team_stats, key_issues)

        # 在报告末尾添加新的分析结果
        if "## 7. 结论" in content:
            # 在结论前插入
            content = content.replace("## 7. 结论", f"{new_section}\n## 7. 结论")
        else:
            # 在文件末尾添加
            content += f"\n{new_section}"

        # 写回文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"已更新失败原因分析报告: {report_file}")

    def _generate_summary_for_report(self, team_stats: Dict, key_issues: Dict) -> str:
        """为失败原因分析报告生成汇总内容"""
        lines = []
        lines.append("## 6.5 进攻一团深度分析结果 (程序分析)")
        lines.append("")
        lines.append("### 6.5.1 核心数据汇总")
        lines.append("")
        lines.append("| 指标 | 数值 | 评估 |")
        lines.append("|------|------|------|")
        lines.append(f"| 团队规模 | {team_stats['团队人数']} 人 | 标准 |")
        lines.append(f"| 团队KD比 | {team_stats['团队KD比']} | {'严重偏低' if team_stats['团队KD比'] < 0.5 else '偏低' if team_stats['团队KD比'] < 1.0 else '正常'} |")
        lines.append(f"| 平均死亡数 | {team_stats['平均死亡数']} 次/人 | {'严重过高' if team_stats['平均死亡数'] > 15 else '过高' if team_stats['平均死亡数'] > 10 else '正常'} |")
        lines.append(f"| 建筑伤害占比 | {team_stats['建筑伤害占比']}% | {'严重不足' if team_stats['建筑伤害占比'] < 30 else '不足' if team_stats['建筑伤害占比'] < 50 else '良好'} |")
        lines.append(f"| 平均拆塔专注度 | {team_stats['平均拆塔专注度']}% | {'严重偏低' if team_stats['平均拆塔专注度'] < 30 else '偏低' if team_stats['平均拆塔专注度'] < 50 else '良好'} |")
        lines.append("")

        lines.append("### 6.5.2 关键问题统计")
        lines.append("")
        lines.append("| 问题类型 | 涉及人数 | 严重程度 |")
        lines.append("|----------|----------|----------|")
        lines.append(f"| 高死亡率问题 | {len(key_issues['高死亡率玩家'])} 人 | {'严重' if len(key_issues['高死亡率玩家']) > 5 else '中等' if len(key_issues['高死亡率玩家']) > 2 else '轻微'} |")
        lines.append(f"| 拆塔效率问题 | {len(key_issues['低效率玩家'])} 人 | {'严重' if len(key_issues['低效率玩家']) > 8 else '中等' if len(key_issues['低效率玩家']) > 4 else '轻微'} |")
        lines.append(f"| 角色配置问题 | {len(key_issues['角色定位问题'])} 人 | {'严重' if len(key_issues['角色定位问题']) > 0 else '无'} |")
        lines.append("")

        # 生成具体的改进建议
        lines.append("### 6.5.3 程序分析结论")
        lines.append("")

        # 根据数据生成结论
        if team_stats['建筑伤害占比'] < 30:
            lines.append("**核心问题**: 进攻一团严重偏离拆塔职责，建筑伤害占比仅" + str(team_stats['建筑伤害占比']) + "%")

        if team_stats['平均死亡数'] > 12:
            lines.append("**生存问题**: 团队平均死亡数过高(" + str(team_stats['平均死亡数']) + "次/人)，无法持续执行战术")

        if len(key_issues['角色定位问题']) > 0:
            lines.append("**配置错误**: " + str(len(key_issues['角色定位问题'])) + "名素问被错误分配到进攻一团，浪费治疗资源")

        lines.append("")
        lines.append("**紧急改进措施**:")
        lines.append("1. 重新配置人员：移除所有素问，补充专业拆塔DPS")
        lines.append("2. 强化拆塔意识：要求所有成员拆塔专注度>70%")
        lines.append("3. 生存技能训练：降低个人死亡数至10次以下")
        lines.append("4. 战术纪律强化：明确进攻一团唯一职责是拆塔推进")

        return "\n".join(lines)


def main():
    """主函数"""
    print("=== 进攻一团表现分析程序 ===\n")

    # 初始化分析器
    analyzer = AttackTeamAnalyzer('merge_XHvsQS.json', '进攻一团名单.txt')

    # 获取进攻一团数据
    attack_team_data = analyzer.get_attack_team_data()

    if not attack_team_data:
        print("错误：未找到进攻一团成员数据")
        return

    print(f"找到进攻一团成员 {len(attack_team_data)} 人")
    print("成员列表:", [p['玩家名字'] for p in attack_team_data])
    print()

    # 生成详细报告
    print("正在生成详细分析报告...")
    detailed_report = analyzer.generate_detailed_report()

    # 保存详细报告
    with open('进攻一团详细分析报告.md', 'w', encoding='utf-8') as f:
        f.write(detailed_report)
    print("详细报告已保存: 进攻一团详细分析报告.md")

    # 更新失败原因分析报告
    print("正在更新失败原因分析报告...")
    analyzer.update_failure_analysis_report('星河若梦失败原因分析报告.md')

    # 显示关键统计
    team_stats = analyzer.calculate_team_stats(attack_team_data)
    key_issues = analyzer.identify_key_issues(attack_team_data)

    print("\n=== 关键统计汇总 ===")
    print(f"团队KD比: {team_stats['团队KD比']}")
    print(f"平均死亡数: {team_stats['平均死亡数']} 次/人")
    print(f"建筑伤害占比: {team_stats['建筑伤害占比']}%")
    print(f"平均拆塔专注度: {team_stats['平均拆塔专注度']}%")

    print(f"\n=== 问题统计 ===")
    print(f"高死亡率玩家: {len(key_issues['高死亡率玩家'])} 人")
    print(f"低拆塔效率玩家: {len(key_issues['低效率玩家'])} 人")
    print(f"角色配置问题: {len(key_issues['角色定位问题'])} 人")

    # 显示最严重的问题玩家
    if key_issues['高死亡率玩家']:
        worst_death = max(key_issues['高死亡率玩家'], key=lambda x: x['死亡数'])
        print(f"\n最高死亡数: {worst_death['玩家']} ({worst_death['死亡数']} 次)")

    if key_issues['低效率玩家']:
        worst_efficiency = min(key_issues['低效率玩家'], key=lambda x: x['拆塔专注度'])
        print(f"最低拆塔专注度: {worst_efficiency['玩家']} ({worst_efficiency['拆塔专注度']}%)")

    print("\n分析完成！")


if __name__ == "__main__":
    main()
